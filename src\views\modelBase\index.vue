<template>
  <div class="media-panel-wrapper">
    <a-form v-if="breadcrumb.length === 3" class="search-form">
      <a-col :span="6">
        <a-form-item label="文件名称" style="padding-left: 10px;">
          <a-input v-model:value="body.file_name" placeholder="请输入文件名称" allow-clear style="width: 200px;" />
        </a-form-item>
      </a-col>
      <!--<a-col :span="6">
        <a-form-item label="飞行器类型">
          <a-input v-model:value="body.payload" placeholder="请输入飞行器类型" allow-clear style="width: 200px;" />
        </a-form-item>
      </a-col>-->
      <a-col :span="6">
        <a-form-item label="创建时间">
          <a-date-picker v-model:value="body.queryTime" :locale="zh_CN" valueFormat="YYYY-MM-DD" placeholder="请选择创建时间"
            style="width: 200px;" />
        </a-form-item>
      </a-col>
      <a-col :span="6">
        <a-form-item>
          <a-button class="ml10" type="primary" @click="search">搜索</a-button>
          <a-button class="ml10" type="primary" @click="reset">重置</a-button>
          <a-button class="ml10" type="error" @click="onDeleteAll" v-permission>批量删除</a-button>
          <a-button class="ml10" type="error" @click="onDownloadAll">批量下载</a-button>
        </a-form-item>
      </a-col>
    </a-form>

    <a-config-provider :locale="zhCN">
      <a-breadcrumb class="bgfff">
        <a-breadcrumb-item v-for="(item, index) in breadcrumb" :key="index" @click="onBreadcrumbClick(item, index)">
          {{ item.name }}
        </a-breadcrumb-item>
      </a-breadcrumb>

      <a-table class="media-table" :scroll="{ x: '100rem' }" :columns="columns" @change="refreshData"
        :rowKey="(row, index) => { return index }" :data-source="mediaData.data" :pagination="paginationProp"
        :row-selection="airportTableLogState.rowSelection">
        <template v-for="col in ['name', 'path']" #[col]="{ text, record }" :key="col">
          <a-tooltip :title="text">
            <a v-if="col === 'name'" @click="onTableItemClick(record)" class="pl10">
              <FolderOpenOutlined v-if="record.file_type == 1" class="fz20" />
              <FileOutlined v-if="record.file_type != 1" class="fz20" />
              {{ text }}
            </a>
            <span v-else>{{ text }}</span>
          </a-tooltip>
        </template>
        <template #original="{ text }">
          {{ text ? '是' : '否' }}
        </template>
        <template #file_size="{ text }">
          {{ text ? text + 'MB' : '' }}
        </template>
        <template #action="{ record }">
          <template v-if="record.file_type != 1">
            <a-tooltip title="预览" v-if="record.file_name.split('.').pop() !== 'DAT'">
              <a class="fz14" style="color:#1890ff" @click="viewMedia(record)">
                <FileSearchOutlined />
                预览
              </a>
            </a-tooltip>

            <a-tooltip title="下载">
              <div v-if="downloadData.some(item => item.file_id === record.file_id)" style="display: flex;">
                <el-progress :percentage="downloadData.find(item => item.file_id === record.file_id).percentage"
                  style="width: 140px;" />
              </div>
              <a v-else class="fz14" style="color: #1890ff;" @click="downloadClick(record)">
                <DownloadOutlined />
                下载
              </a>
            </a-tooltip>
            <span v-permission>
              <a-tooltip title="删除" v-permission>
                <a-popconfirm title="是否确定删除该数据?" @confirm="onDelete(record)" v-permission>
                  <DeleteTwoTone v-permission />
                  <span style="color: #1890ff;cursor: pointer;" v-permission> 删除</span>
                </a-popconfirm>
              </a-tooltip>
            </span>
          </template>
          
          <!-- 文件夹操作 -->
          <template v-else-if="record.level === 2">
            <a-tooltip title="下载">
              <div v-if="downloadData.some(item => item.file_id === record.file_id)" style="display: flex;">
                <el-progress :percentage="downloadData.find(item => item.file_id === record.file_id).percentage"
                  style="width: 140px;" />
              </div>
              <a v-else class="fz14" style="color: #1890ff;" @click="downloadFolderClick(record)">
                <DownloadOutlined />
                下载
              </a>
            </a-tooltip>
            <!-- <span v-permission>
              <a-tooltip title="删除" v-permission>
                <a-popconfirm title="是否确定删除该数据?" @confirm="onDelete(record)" v-permission>
                  <DeleteTwoTone v-permission />
                  <span style="color: #1890ff;cursor: pointer;" v-permission> 删除</span>
                </a-popconfirm>
              </a-tooltip>
            </span> -->
          </template>
        </template>
      </a-table>
    </a-config-provider>

    <el-dialog v-model="previewVisible" title="文件预览" width="1000px" style="height: auto;" :close-on-click-modal="false"
      :before-close="handleCancel" @closed="handleDialogClosed">
      <video v-if="previewType === 'MP4'" controls preload="auto" ref="videoPlayer" @loadedmetadata="playVideo"
        style="width: 968px;max-height: 500px;" :src="previewUrl">
        <!--<source :src="previewUrl" type="video/mp4">-->
      </video>
      <img v-else :src="previewUrl" alt="Image" style="width: 968px;max-height: 500px;" />
      <div v-if="previewType === 'MP4' && previewUrl" class="rate">
        <button @click="changePlaybackRate(1)">1x</button>
        <button @click="changePlaybackRate(2)">2x</button>
        <button @click="changePlaybackRate(3)">3x</button>
        <button @click="changePlaybackRate(5)">5x</button>
      </div>
    </el-dialog>
    <el-image-viewer v-if="showPreview" :url-list="srcList" show-progress :initial-index="0"
      @close="showPreview = false"></el-image-viewer>
  </div>
</template>

<script setup>
import store from '@/store';
import { getToken } from 'utils/auth';
import { useRouter } from 'vue-router';
import { ELocalStorageKey } from '@/api/enum';
import { CURRENT_CONFIG } from '@/config/dijconfig';
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, onMounted, watch, computed } from 'vue';
import zh_CN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { downloadMediaFile, getFileSize, deleteMediaFiles, getFilesLevelPage, getMediaLocal, downloadFolderAsZip } from '@/api/media/index.js';
import { DownloadOutlined, DeleteTwoTone, FileSearchOutlined, FolderOpenOutlined, FileOutlined } from '@ant-design/icons-vue';
import { getWorkspaceId } from '@/utils/storage'
let ids = ref([]);
let file_id = ref('');
let percentage = ref(0);
let isDownloading = ref(false);
let workspaceId = getWorkspaceId();
let previewUrl = ref(''); // 预览文件URL
let previewType = ref(''); // 预览文件类型
let srcList = ref([]); // 预览图片列表
let showPreview = ref(false); // 是否显示预览
let videoPlayer = ref(null);
let previewVisible = ref(false);
let airportTableLogState = reactive({
  rowSelection: {
    columnWidth: 15,
    selectedRowKeys: [],
    onChange: (selectedRowKeys, selectedRows) => {
      airportTableLogState.rowSelection.selectedRowKeys = selectedRowKeys
      ids.value = []
      selectedRows.forEach((item) => {
        ids.value.push(item.file_id)
      })
    },
  }
})
let breadcrumb = ref([{ name: '全部文件' }])
let columns = [
  {
    title: '文件名称',
    dataIndex: 'file_name',
    slots: { customRender: 'name' },
    width: '30rem',
  },
  {
    title: '设备编号',
    dataIndex: 'drone',
    ellipsis: true,
  },
  {
    title: '负载',
    dataIndex: 'payload',
    ellipsis: true,
    width: 300,
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    ellipsis: true,
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    ellipsis: true,
    width: 120,
    customRender: ({ text }) => {
      return `${text ? text + 'MB' : '--'}`;
    },
  },
  {
    title: '操作',
    slots: { customRender: 'action' },
    fixed: 'right',
    width: 200,
  },
];
let body = reactive({
  // payload: null,
  file_name: null,
  queryTime: null,
});
let paginationProp = reactive({
  pageSizeOptions: ['10', '20', '50', '100'],
  showQuickJumper: true,
  showSizeChanger: true,
  pageSize: 10,
  current: 1,
  total: 0,
});
let mediaData = reactive({
  data: [{
    file_name: '平台-3D可见光重建任务',
    file_url: '/models/pc/0/terra_b3dms/tileset.json',
    // file_url: '/showmode/terra_b3dms/tileset.json',
    file_type: 2,
    file_id: '123',
    size: 123,
    create_time: '2021-01-01',
    payload: '123',
    drone: '123'
  },
  {
    file_name: '平台-WebODM GLB模型',
    file_url: '/models/webodmModel/odm_texturing/odm_textured_model_geo.glb',
    file_type: 2,
    file_id: '124',
    size: 123,
    create_time: '2021-01-01',
    payload: '123',
    drone: '123'
  },
  {
    file_name: '三魁水可见光重建任务',
    file_url: '/models/pc/0/terra_b3dms2/tileset.json',
    file_type: 2,
    file_id: '123',
    size: 123,
    create_time: '2021-01-01',
    payload: '123',
    drone: '123',
  }],
});

// 添加层级页码记录
let levelPagination = reactive({});

let downloadData = computed(() => {
  return store.state.common.downloadData;
});

onMounted(() => {
  getFiles();
});

watch(store.state.common.downloadData, (data) => {
  if (data.file_id !== '') {
    percentage.value = data.percentage
  }
}, { deep: true }
);

watch(breadcrumb, () => {
  ids.value = [];
  airportTableLogState.rowSelection.selectedRowKeys = [];
}, { deep: true }
);

async function getFiles() {
  const prop = breadcrumb.value.length !== 1 ? breadcrumb.value[breadcrumb.value.length - 1] : null
  const res = await getFilesLevelPage(workspaceId, {
    ...prop,
    workspaceId,
    fileName: body.file_name,
    queryTime: body.queryTime,
    size: paginationProp.pageSize,
    current: paginationProp.current,
  })
  // mediaData.data = res.data.data.records;
  paginationProp.total = res.data.data.total;
  paginationProp.current = res.data.data.current;
  // 初始化加载更新进度条
  const savedProgress = store.state.common.downloadData;
  if (savedProgress) {
    file_id.value = savedProgress.file_id;
    percentage.value = savedProgress.percentage;
  }
}

function search() {
  getFiles();
}

function reset() {
  body.file_name = null
  // body.payload = null
  body.queryTime = null
  getFiles();
}

function getStringAfter(sourceString, searchString) {
  let parts = sourceString.split(searchString);
  if (parts.length > 1) {
    return parts.slice(1).join(''); // 返回searchString后面的部分
  }
  return ''; // 如果searchString不存在，返回空字符串
}

function refreshData(page) {
  if (page.pageSize != paginationProp.pageSize) {
    page.current = 1
  }
  paginationProp.current = page.current
  paginationProp.pageSize = page.pageSize
  getFiles();
}

// 文件预览
async function viewMedia(media) {
  let workspaceId = localStorage.getItem(ELocalStorageKey.WorkspaceId) || ''
  let fileSize = await getFileSize(workspaceId, media.file_id);
  if (fileSize === 0) {
    ElMessage({ type: 'error', message: '文件已销毁！' })
    return
  }
  previewType.value = getStringAfter(media.file_name, '.').toUpperCase();
  let url = CURRENT_CONFIG.baseURL
  if (previewType.value === 'MP4') {
    downloadVideo.value = false;
    // setVideo(media)
    previewUrl.value = `${url}media/api/v1/files/${workspaceId}/file/${media.file_id}/playMp4/v2?HzTech-Auth=bearer ` + getToken()
    previewVisible.value = true;
  } else {
    previewUrl.value = `${url}media/api/v1/files/${workspaceId}/file/${media.file_id}/url?HzTech-Auth=bearer ` + getToken()
    srcList.value = [previewUrl.value];
    showPreview.value = true;
  }
}

// 停止视频加载或播放
const abortVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause();
    videoPlayer.value.src = '';
  }
};

// 是否中断下载
const downloadVideo = ref(false)

/**
 *  设置视频
 * @param media
 * @returns {Promise<void>}
 */
async function setVideo(media) {
  return new Promise(async (resolve, reject) => {
    try {
      const fileSize = media.size * 1024 * 1024;
      const chunkSize = 1024 * 1024 * 5;
      const chunks = calculateChunks(fileSize, chunkSize);
      let fileBlobs = [];
      const token = getToken();
      for (let i = 0; i < chunks.length; i++) {
        const blobs = await getMediaLocal(workspaceId, media.file_id, chunks[i], token);
        fileBlobs.push(blobs);
        if (downloadVideo.value) {
          break;
        }
      }
      const blob = new Blob(fileBlobs, { type: 'video/mp4' });
      const url = URL.createObjectURL(blob);
      previewUrl.value = url;
      resolve();
    } catch (error) {
      reject(error);
    } finally {

    }
  });
}


// 视频播放
const playVideo = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play();
  }
};

// 设置播放倍速
const changePlaybackRate = (rate) => {
  videoPlayer.value.playbackRate = rate;
}

// 关闭弹窗前的回调方法
const handleCancel = () => {
  previewUrl.value = '';
  previewType.value = '';
  previewVisible.value = false;
  abortVideo();
};

// 关闭弹窗清除视频加载
const handleDialogClosed = () => {
  downloadVideo.value = true;
  if (videoPlayer.value) {
    videoPlayer.value.playbackRate = 1.0; // 重置播放倍速
    videoPlayer.value.currentTime = 0; // 重置播放进度
    videoPlayer.value.pause(); // 暂停视频
    videoPlayer.value.src = ''; // 清除视频源，防止继续加载
    videoPlayer.value.load(); // 重新加载视频，确保清除所有缓存数据
  }
};

// 删除
async function onDelete(row) {
  let params = {
    file_ids: row.file_id,
    workspace_id: workspaceId,
  }
  const res = await deleteMediaFiles(params)
  if (res.data.code === 0) {
    setTimeout(() => {
      getFiles()
      ElMessage({
        type: 'success',
        message: '删除成功！',
      })
    }, 1000);
  } else {
    ElMessage({
      type: 'error',
      message: '删除失败！',
    })
  }
}

// 批量删除
function onDeleteAll() {
  if (ids.value.length > 0) {
    ElMessageBox.confirm('是否确定删除该文件数据?',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      let params = {
        file_ids: ids.value.join(','),
        workspace_id: workspaceId,
      }
      deleteMediaFiles(params)
      setTimeout(() => {
        airportTableLogState.rowSelection.selectedRowKeys = []
        getFiles()
        ElMessage({
          type: 'success',
          message: '删除成功！',
        })
      }, 1000);
    })
  } else {
    ElMessage({
      type: 'error',
      message: '请选择要删除的数据！',
    })
  }
}

async function downloadSingleFile(media) {
  return new Promise(async (resolve, reject) => {
    try {
      const fileSize = await getFileSize(workspaceId, media.file_id);
      if (fileSize == 0) {
        throw new Error('文件已销毁！');
      }
      const chunkSize = 1024 * 1024 * 2;
      const chunks = calculateChunks(fileSize, chunkSize);
      let fileBlobs = [];
      for (let i = 0; i < chunks.length; i++) {
        const blobs = await downloadMediaFile(workspaceId, media.file_id, chunks[i]);
        fileBlobs.push(blobs);
        let start = i + 1;
        let end = chunks.length;
        let num = (start / end) * 100;

        // 更新下载进度
        const progressIndex = store.state.common.downloadData.findIndex(item => item.file_id === media.file_id);
        if (progressIndex !== -1) {
          store.state.common.downloadData[progressIndex].percentage = parseFloat(num.toFixed(2));
        }

        percentage.value = parseFloat(num.toFixed(2));
      }
      // 创建文件链接并触发下载
      const blob = new Blob(fileBlobs, { type: 'application/octet-stream' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', media.file_name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      resolve();
    } catch (error) {
      reject(error);
    } finally {
      isDownloading.value = false;
      // 清理操作
      const index = store.state.common.downloadData.findIndex(item => item.file_id === media.file_id);
      if (index !== -1) {
        store.state.common.downloadData.splice(index, 1);
      }
    }
  });
}

// 行单击下载
function downloadClick(row) {
  store.state.common.downloadQueue.push({ file_id: row.file_id, file_name: row.file_name });
  const existingProgress = store.state.common.downloadData.find(item => item.file_id === row.file_id);
  if (!existingProgress) {
    store.state.common.downloadData.push({ file_id: row.file_id, percentage: 0 });
  }
  if (!isDownloading.value) {
    isDownloading.value = true;
    downloadMedias();
  } else {
    ElMessage({
      type: 'success',
      message: '添加下载队列成功！',
    });
  }
}

// 批量下载
function onDownloadAll() {
  if (ids.value.length > 0) {
    ElMessageBox.confirm('是否确定下载选中文件数据?',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      // 匹配选中数据
      const matchedFiles = mediaData.data.filter(file => ids.value.includes(file.file_id));
      // 筛选文件id和文件名称
      const selectedFiles = matchedFiles.map(({ file_id, file_name }) => ({ file_id, file_name }));
      // 开始下载多文件
      selectedFiles.forEach(item => {
        store.state.common.downloadData.push({ file_id: item.file_id, percentage: 0 });
      })
      store.state.common.downloadQueue = selectedFiles
      downloadMedias();
    })
  } else {
    ElMessage({
      type: 'error',
      message: '请选择要下载的数据！',
    })
  }
}

// 多文件下载
function downloadMedias() {
  async function downloadNext() {
    if (store.state.common.downloadQueue.length === 0) {
      // 下载完成清空选中状态和选中ids
      ids.value = [];
      airportTableLogState.rowSelection.selectedRowKeys = [];
      return;
    }
    const media = store.state.common.downloadQueue.shift();
    try {
      await downloadSingleFile(media);
      await downloadNext(); // 递归调用下载下一个文件
    } catch (error) {
      console.error('文件下载失败:', error);
      await downloadNext(); // 即使失败也继续下载队列中的下一个文件
    }
  }

  downloadNext();
}

// 计算当前分块
function calculateChunks(fileSize, chunkSize) {
  const chunks = [];
  let start = 0;
  while (start < fileSize) {
    const end = Math.min(start + chunkSize - 1, fileSize - 1);
    chunks.push({ start, end });
    start = end + 1;
  }
  return chunks;
}

const router = useRouter()
/**
 * 文件点击
 */
function onTableItemClick(record) {
  router.push({
    path: '/modelBase/showModel',
    query: {
      file_url: record.file_url
    }
  })
  return
  if (record.file_type !== 1) {
    return
  }
  if (breadcrumb.value.findIndex(v => v.name == record.file_name) != -1) {
    return
  }

  // 保存当前层级的分页信息
  const currentLevel = breadcrumb.value.length;
  levelPagination[currentLevel] = {
    pageSize: paginationProp.pageSize,
    current: paginationProp.current
  };

  paginationProp.pageSize = 10;
  paginationProp.current = 1
  breadcrumb.value.push({
    name: record.file_name,
    ...record,
    fileId: record.file_id,
    file_name: null,
  })
  getFiles();
}

/**
 * 面包屑切换
 * @param item
 */
function onBreadcrumbClick(item, index) {
  // 恢复目标层级的分页信息
  const targetLevel = index + 1;
  if (levelPagination[targetLevel]) {
    paginationProp.pageSize = levelPagination[targetLevel].pageSize;
    paginationProp.current = levelPagination[targetLevel].current;
  } else {
    paginationProp.pageSize = 10;
    paginationProp.current = 1;
  }

  breadcrumb.value.splice(index + 1, breadcrumb.value.length - index - 1);
  getFiles();
}

// 下载文件夹
async function downloadFolderClick(folder) {
  try {
    // 添加到下载队列并显示进度条
    store.state.common.downloadData.push({ file_id: folder.file_id, percentage: 0 });
    
    // 调用下载文件夹API，添加进度监听回调
    const blob = await downloadFolderAsZip(workspaceId, folder.file_id, (percentage) => {
      // 实时更新下载进度
      const progressIndex = store.state.common.downloadData.findIndex(item => item.file_id === folder.file_id);
      if (progressIndex !== -1) {
        store.state.common.downloadData[progressIndex].percentage = percentage;
      }
    }, folder.size);
    
    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${folder.file_name}.zip`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    // 下载完成后移除进度条
    setTimeout(() => {
      const index = store.state.common.downloadData.findIndex(item => item.file_id === folder.file_id);
      if (index !== -1) {
        store.state.common.downloadData.splice(index, 1);
      }
    }, 1000);
    
    ElMessage({
      type: 'success',
      message: '文件夹下载成功！',
    });
  } catch (error) {
    console.error('文件夹下载失败:', error);
    
    // 移除进度条
    const index = store.state.common.downloadData.findIndex(item => item.file_id === folder.file_id);
    if (index !== -1) {
      store.state.common.downloadData.splice(index, 1);
    }
    
    ElMessage({
      type: 'error',
      message: '文件夹下载失败！',
    });
  }
}
</script>

<style lang="scss" scoped>
.media-panel-wrapper {
  width: 100%;
  padding: 0 16px 16px 16px;

  .media-table {
    background: #fff;
  }
}

.rate {
  display: flex;
  justify-content: space-between;
  width: 150px;
}

.search-form {
  display: flex;
  background-color: #fff;
  height: 50px;
  padding-top: 10px;
}

.bgfff {
  background: #fff;
  padding-left: 20px;
  height: 40px;
  line-height: 40px;
}

.pl10 {
  padding-left: 10px;
}

.ml10 {
  margin-left: 10px;
}

.fz20 {
  font-size: 20px;
}
</style>